#!/usr/bin/env python
import os
import sys
import django
import json

# Setup Django
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from game.models import GameSession, Message

def test_fetch_next_task():
    """Test the fetch_next_task endpoint for the problematic session"""
    
    # Get the problematic session (46)
    try:
        session = GameSession.objects.get(id=46)
        user = session.user
    except GameSession.DoesNotExist:
        print("❌ Session 46 not found")
        return
    
    print(f'=== TESTING FETCH_NEXT_TASK FOR SESSION {session.id} ===')
    print(f'Current state: role={session.current_role}, task={session.current_task}, completed={session.role_challenges_completed}')
    print(f'Next task pending: {getattr(session, "next_task_pending", "N/A")}')
    print()
    
    # Create the Django test client
    client = Client()
    
    # Simulate login
    client.force_login(user)
    
    # Call fetch_next_task endpoint
    print('=== CALLING FETCH_NEXT_TASK ENDPOINT ===')
    
    response = client.get('/game/api/fetch_next_task/')
    
    print(f'Response status: {response.status_code}')
    
    if response.status_code == 200:
        response_data = response.json()
        print(f'Response status: {response_data.get("status", "unknown")}')
        print(f'Response message: {response_data.get("message", "no message")}')
        print()
        
        if response_data.get("status") == "success":
            message_data = response_data.get("message", {})
            print(f'=== TASK MESSAGE DETAILS ===')
            print(f'Task ID: {message_data.get("task_id", "N/A")}')
            print(f'Sender: {message_data.get("sender", "N/A")}')
            print(f'Is challenge: {message_data.get("is_challenge", "N/A")}')
            print(f'Text preview: {message_data.get("text", "")[:100]}...')
            print()
            
            # Check if a new message was created
            session.refresh_from_db()
            print(f'=== SESSION STATE AFTER FETCH ===')
            print(f'Current task: {session.current_task}')
            print(f'Role challenges completed: {session.role_challenges_completed}')
            print()
            
            # Check what messages exist now
            print(f'=== MESSAGES AFTER FETCH ===')
            messages = session.messages.filter(is_challenge=True).order_by('timestamp')
            for msg in messages:
                print(f'  - ID {msg.id}: {msg.task_id} - {msg.text[:50]}...')
            print()
            
            # Check if customer_service message was created
            customer_service_messages = session.messages.filter(task_id='customer_service', is_challenge=True)
            if customer_service_messages.exists():
                print('✅ SUCCESS: customer_service challenge message was created!')
                for msg in customer_service_messages:
                    print(f'   Message ID: {msg.id}, Text: {msg.text[:100]}...')
            else:
                print('❌ STILL NO customer_service challenge message')
        else:
            print(f'❌ Fetch failed: {response_data.get("message", "unknown error")}')
    else:
        print(f'❌ Request failed with status {response.status_code}')
        print(f'Response: {response.content.decode()}')

if __name__ == '__main__':
    test_fetch_next_task()
