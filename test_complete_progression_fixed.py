#!/usr/bin/env python
import os
import sys
import django
import json

# Setup Django
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from game.models import GameSession, Message

def test_complete_progression():
    """Test the complete task progression flow with the fix"""
    
    # Create a test user
    user, created = User.objects.get_or_create(username='test_progression_user')
    
    # Create a fresh session
    session = GameSession.objects.create(
        user=user,
        current_role='junior_assistant',
        current_task='job_analysis',
        current_manager='manager',
        role_challenges_completed=0,
        challenges_completed=0,
        performance_score=0
    )
    
    print(f'=== TESTING COMPLETE TASK PROGRESSION ===')
    print(f'Session {session.id}')
    print(f'Starting state: role={session.current_role}, task={session.current_task}, completed={session.role_challenges_completed}')
    print()
    
    # Create the Django test client
    client = Client()
    client.force_login(user)
    
    # Step 1: Get the first task (job_analysis)
    print('=== STEP 1: FETCH FIRST TASK (job_analysis) ===')
    response = client.get('/game/api/fetch_first_task/')
    if response.status_code == 200:
        data = response.json()
        if data.get('status') == 'success':
            print(f'✅ First task fetched: {data["message"]["task_id"]}')
        else:
            print(f'❌ Failed to fetch first task: {data.get("message")}')
    print()
    
    # Step 2: Complete job_analysis task
    print('=== STEP 2: COMPLETE job_analysis TASK ===')
    payload = {
        'prompt': 'This is a test response for job analysis task',
        'current_task_id': 'job_analysis'
    }
    response = client.post('/game/api/submit_prompt/', 
                          data=json.dumps(payload), 
                          content_type='application/json')
    
    if response.status_code == 200:
        data = response.json()
        print(f'Task completion status: {data.get("status")}')
        print(f'Current task after completion: {data.get("current_task")}')
        print(f'Role challenges completed: {data.get("role_challenges_completed")}')
        print(f'Next task pending: {data.get("next_task_pending")}')
        
        # Check session state
        session.refresh_from_db()
        print(f'Session current_task: {session.current_task}')
        print(f'Session role_challenges_completed: {session.role_challenges_completed}')
    print()
    
    # Step 3: Fetch the next task (should be customer_service)
    print('=== STEP 3: FETCH NEXT TASK (should be customer_service) ===')
    response = client.get('/game/api/fetch_next_task/')
    if response.status_code == 200:
        data = response.json()
        if data.get('status') == 'success':
            message_data = data.get('message', {})
            print(f'✅ Next task fetched: {message_data.get("task_id")}')
            print(f'Sender: {message_data.get("sender")}')
            print(f'Text preview: {message_data.get("text", "")[:100]}...')
        else:
            print(f'❌ Failed to fetch next task: {data.get("message")}')
    print()
    
    # Step 4: Check all messages
    print('=== STEP 4: CHECK ALL CHALLENGE MESSAGES ===')
    session.refresh_from_db()
    messages = session.messages.filter(is_challenge=True).order_by('timestamp')
    for i, msg in enumerate(messages, 1):
        print(f'  {i}. ID {msg.id}: {msg.task_id} - {msg.text[:50]}...')
    print(f'Total challenge messages: {messages.count()}')
    print()
    
    # Step 5: Complete customer_service task
    print('=== STEP 5: COMPLETE customer_service TASK ===')
    payload = {
        'prompt': 'This is a test response for customer service task',
        'current_task_id': 'customer_service'
    }
    response = client.post('/game/api/submit_prompt/', 
                          data=json.dumps(payload), 
                          content_type='application/json')
    
    if response.status_code == 200:
        data = response.json()
        print(f'Task completion status: {data.get("status")}')
        print(f'Current task after completion: {data.get("current_task")}')
        print(f'Role challenges completed: {data.get("role_challenges_completed")}')
        print(f'Next task pending: {data.get("next_task_pending")}')
    print()
    
    # Step 6: Fetch the final task (should be onboarding_checklist)
    print('=== STEP 6: FETCH FINAL TASK (should be onboarding_checklist) ===')
    response = client.get('/game/api/fetch_next_task/')
    if response.status_code == 200:
        data = response.json()
        if data.get('status') == 'success':
            message_data = data.get('message', {})
            print(f'✅ Final task fetched: {message_data.get("task_id")}')
        else:
            print(f'❌ Failed to fetch final task: {data.get("message")}')
    print()
    
    # Final check: All tasks should be present
    print('=== FINAL CHECK: ALL TASKS PRESENT ===')
    session.refresh_from_db()
    messages = session.messages.filter(is_challenge=True).order_by('timestamp')
    
    expected_tasks = ['job_analysis', 'customer_service', 'onboarding_checklist']
    found_tasks = [msg.task_id for msg in messages]
    
    print(f'Expected tasks: {expected_tasks}')
    print(f'Found tasks: {found_tasks}')
    
    if set(expected_tasks) == set(found_tasks):
        print('✅ SUCCESS: All tasks are present!')
    else:
        missing = set(expected_tasks) - set(found_tasks)
        extra = set(found_tasks) - set(expected_tasks)
        if missing:
            print(f'❌ Missing tasks: {missing}')
        if extra:
            print(f'❌ Extra tasks: {extra}')
    
    # Clean up
    session.delete()
    user.delete()

if __name__ == '__main__':
    test_complete_progression()
