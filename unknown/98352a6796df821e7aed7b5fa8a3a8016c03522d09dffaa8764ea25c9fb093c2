#!/usr/bin/env python
import os
import sys
import django
import json

# Setup Django
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from game.models import GameSession, Message

def test_progression_with_good_prompts():
    """Test task progression with prompts that should pass"""
    
    # Create a test user
    user, created = User.objects.get_or_create(username='test_good_prompts_user')
    
    # Create a fresh session
    session = GameSession.objects.create(
        user=user,
        current_role='junior_assistant',
        current_task='job_analysis',
        current_manager='manager',
        role_challenges_completed=0,
        challenges_completed=0,
        performance_score=0
    )
    
    print(f'=== TESTING TASK PROGRESSION WITH GOOD PROMPTS ===')
    print(f'Session {session.id}')
    print()
    
    # Create the Django test client
    client = Client()
    client.force_login(user)
    
    # Step 1: Get the first task
    print('=== STEP 1: FETCH FIRST TASK ===')
    response = client.get('/game/api/fetch_first_task/')
    if response.status_code == 200:
        data = response.json()
        if data.get('status') == 'success':
            print(f'✅ First task fetched: {data["message"]["task_id"]}')
    print()
    
    # Step 2: Complete job_analysis with a good prompt
    print('=== STEP 2: COMPLETE job_analysis WITH GOOD PROMPT ===')
    good_job_analysis_prompt = """
    Please analyze the Software Engineer position at Rwenzori Innovations. 
    
    Based on the job description, I need you to:
    1. Identify the key technical skills required (programming languages, frameworks, tools)
    2. List the main responsibilities and daily tasks
    3. Determine the experience level needed (junior, mid-level, senior)
    4. Highlight any special qualifications or certifications mentioned
    5. Analyze the work environment and team structure
    
    Please format your analysis in clear sections with bullet points for easy reading.
    """
    
    payload = {
        'prompt': good_job_analysis_prompt,
        'current_task_id': 'job_analysis'
    }
    response = client.post('/game/api/submit_prompt/', 
                          data=json.dumps(payload), 
                          content_type='application/json')
    
    if response.status_code == 200:
        data = response.json()
        print(f'Task completion status: {data.get("status")}')
        print(f'Current task after completion: {data.get("current_task")}')
        print(f'Role challenges completed: {data.get("role_challenges_completed")}')
        print(f'Next task pending: {data.get("next_task_pending")}')
        
        # Check if we advanced
        if data.get("current_task") != "job_analysis":
            print(f'✅ Task progression worked! Advanced to: {data.get("current_task")}')
        else:
            print(f'❌ Still on job_analysis task')
    print()
    
    # Step 3: Fetch the next task if progression happened
    print('=== STEP 3: FETCH NEXT TASK ===')
    response = client.get('/game/api/fetch_next_task/')
    if response.status_code == 200:
        data = response.json()
        if data.get('status') == 'success':
            message_data = data.get('message', {})
            print(f'✅ Next task fetched: {message_data.get("task_id")}')
            
            if message_data.get("task_id") == "customer_service":
                print('🎉 SUCCESS: customer_service task was fetched correctly!')
            else:
                print(f'❌ Expected customer_service, got: {message_data.get("task_id")}')
    print()
    
    # Step 4: Check all messages
    print('=== STEP 4: CHECK ALL CHALLENGE MESSAGES ===')
    session.refresh_from_db()
    messages = session.messages.filter(is_challenge=True).order_by('timestamp')
    for i, msg in enumerate(messages, 1):
        print(f'  {i}. ID {msg.id}: {msg.task_id} - {msg.text[:50]}...')
    print(f'Total challenge messages: {messages.count()}')
    
    # Check specifically for customer_service
    customer_service_messages = session.messages.filter(task_id='customer_service', is_challenge=True)
    if customer_service_messages.exists():
        print('✅ customer_service challenge message exists!')
    else:
        print('❌ No customer_service challenge message found')
    print()
    
    # Clean up
    session.delete()
    user.delete()

if __name__ == '__main__':
    test_progression_with_good_prompts()
