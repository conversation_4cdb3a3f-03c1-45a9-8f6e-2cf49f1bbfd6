#!/usr/bin/env python
import os
import sys
import django
import json

# Setup Django
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from game.models import GameSession, Message

def test_customer_service_fetch():
    """Test what happens when we manually call fetch_next_task for customer_service"""
    
    # Create a test user
    user, created = User.objects.get_or_create(username='test_customer_service_user')
    
    # Create a session that's on customer_service task (simulating the state after job_analysis completion)
    session = GameSession.objects.create(
        user=user,
        current_role='junior_assistant',
        current_task='customer_service',  # This is where we should be after job_analysis
        current_manager='manager',
        role_challenges_completed=1,  # We completed job_analysis
        challenges_completed=1,
        performance_score=20
    )
    
    # Set next_task_pending to True (this is what happens after task completion)
    session.next_task_pending = True
    session.save()
    
    print(f'=== CREATED TEST SESSION FOR CUSTOMER_SERVICE ===')
    print(f'Session {session.id}')
    print(f'State: role={session.current_role}, task={session.current_task}, completed={session.role_challenges_completed}')
    print(f'Next task pending: {getattr(session, "next_task_pending", "N/A")}')
    print()
    
    # Create the Django test client
    client = Client()
    
    # Simulate login
    client.force_login(user)
    
    # Check initial messages
    print(f'=== INITIAL MESSAGES ===')
    messages = session.messages.filter(is_challenge=True).order_by('timestamp')
    for msg in messages:
        print(f'  - ID {msg.id}: {msg.task_id} - {msg.text[:50]}...')
    print(f'Total challenge messages: {messages.count()}')
    print()
    
    # Call fetch_next_task endpoint
    print('=== CALLING FETCH_NEXT_TASK ENDPOINT ===')
    
    response = client.get('/game/api/fetch_next_task/')
    
    print(f'Response status: {response.status_code}')
    
    if response.status_code == 200:
        response_data = response.json()
        print(f'Response status: {response_data.get("status", "unknown")}')
        
        if response_data.get("status") == "success":
            message_data = response_data.get("message", {})
            print(f'=== FETCHED TASK MESSAGE ===')
            print(f'Task ID: {message_data.get("task_id", "N/A")}')
            print(f'Sender: {message_data.get("sender", "N/A")}')
            print(f'Is challenge: {message_data.get("is_challenge", "N/A")}')
            print(f'Text preview: {message_data.get("text", "")[:100]}...')
            print()
            
            # Check if a new message was created
            session.refresh_from_db()
            print(f'=== SESSION STATE AFTER FETCH ===')
            print(f'Current task: {session.current_task}')
            print(f'Next task pending: {getattr(session, "next_task_pending", "N/A")}')
            print()
            
            # Check what messages exist now
            print(f'=== MESSAGES AFTER FETCH ===')
            messages = session.messages.filter(is_challenge=True).order_by('timestamp')
            for msg in messages:
                print(f'  - ID {msg.id}: {msg.task_id} - {msg.text[:50]}...')
            print(f'Total challenge messages: {messages.count()}')
            print()
            
            # Check specifically for customer_service message
            customer_service_messages = session.messages.filter(task_id='customer_service', is_challenge=True)
            if customer_service_messages.exists():
                print('✅ SUCCESS: customer_service challenge message was created!')
                for msg in customer_service_messages:
                    print(f'   Message ID: {msg.id}')
                    print(f'   Text: {msg.text[:200]}...')
            else:
                print('❌ NO customer_service challenge message found')
        else:
            print(f'❌ Fetch failed: {response_data.get("message", "unknown error")}')
    else:
        print(f'❌ Request failed with status {response.status_code}')
        print(f'Response: {response.content.decode()}')
    
    # Clean up
    session.delete()
    user.delete()

if __name__ == '__main__':
    test_customer_service_fetch()
