#!/usr/bin/env python
import os
import sys
import django

# Setup Django
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

from game.models import GameSession, Message
from game.all_role_tasks import get_all_role_tasks

def debug_double_progression():
    """Debug the double task progression issue"""
    
    # Find the most recent junior_assistant session
    sessions = GameSession.objects.filter(current_role='junior_assistant').order_by('-id')
    
    if not sessions:
        print("No junior_assistant sessions found")
        return
    
    session = sessions[0]
    print(f'=== DEBUGGING DOUBLE PROGRESSION ISSUE ===')
    print(f'Session {session.id}')
    print(f'Current state: role={session.current_role}, task={session.current_task}, completed={session.role_challenges_completed}')
    print()
    
    # Get all tasks for junior_assistant role
    role_tasks = get_all_role_tasks('junior_assistant')
    print('Junior Assistant Tasks:')
    for i, task in enumerate(role_tasks):
        print(f'  {i}: {task["id"]} (manager: {task["manager"]})')
    print()
    
    # Check what messages exist for each task
    print('=== MESSAGE ANALYSIS ===')
    for task in role_tasks:
        task_messages = session.messages.filter(task_id=task['id'], is_challenge=True)
        count = task_messages.count()
        print(f'  {task["id"]}: {count} messages')
        if count > 0:
            for msg in task_messages:
                print(f'    - ID {msg.id}: {msg.text[:50]}... (timestamp: {msg.timestamp})')
    print()
    
    # Simulate the EXACT logic from views.py lines 1088-1104
    print('=== SIMULATING VIEWS.PY TASK PROGRESSION LOGIC ===')
    
    # This is the current state
    current_role = session.current_role
    server_current_task_id = session.current_task  # This is the FIXED logic
    role_challenges_completed = session.role_challenges_completed
    
    print(f'Current role: {current_role}')
    print(f'Server current task ID: {server_current_task_id}')
    print(f'Role challenges completed: {role_challenges_completed}')
    print()
    
    # Get tasks for current role
    tasks_for_current_role = get_all_role_tasks(current_role)
    
    # Find current task index using server task ID (the fix)
    current_task_index_in_role = next((i for i, t_obj in enumerate(tasks_for_current_role) if t_obj["id"] == server_current_task_id), -1)
    next_task_index_in_role = current_task_index_in_role + 1
    
    print(f'Current task index in role: {current_task_index_in_role}')
    print(f'Next task index in role: {next_task_index_in_role}')
    print()
    
    if next_task_index_in_role < len(tasks_for_current_role):
        next_task_details = tasks_for_current_role[next_task_index_in_role]
        print(f'Next task would be: {next_task_details["id"]} (manager: {next_task_details["manager"]})')
        
        # Check if this matches what we see in the database
        expected_task = next_task_details["id"]
        actual_task = session.current_task
        
        if expected_task == actual_task:
            print(f'✅ LOGIC CORRECT: Expected {expected_task}, got {actual_task}')
        else:
            print(f'❌ LOGIC MISMATCH: Expected {expected_task}, but database shows {actual_task}')
    else:
        print('❌ No next task available (end of role)')
    print()
    
    # Check if there's evidence of double progression
    print('=== CHECKING FOR DOUBLE PROGRESSION EVIDENCE ===')
    
    # Look for patterns that indicate double progression
    all_messages = session.messages.all().order_by('timestamp')
    task_messages = [msg for msg in all_messages if msg.is_challenge and msg.task_id]
    
    print('Task messages in chronological order:')
    for i, msg in enumerate(task_messages):
        print(f'  {i+1}. {msg.task_id} (ID: {msg.id}, timestamp: {msg.timestamp})')
    print()
    
    # Check for missing customer_service
    has_job_analysis = any(msg.task_id == 'job_analysis' for msg in task_messages)
    has_customer_service = any(msg.task_id == 'customer_service' for msg in task_messages)
    has_onboarding_checklist = any(msg.task_id == 'onboarding_checklist' for msg in task_messages)
    
    print('Task presence check:')
    print(f'  job_analysis: {"✅" if has_job_analysis else "❌"}')
    print(f'  customer_service: {"✅" if has_customer_service else "❌"}')
    print(f'  onboarding_checklist: {"✅" if has_onboarding_checklist else "❌"}')
    print()
    
    if has_job_analysis and has_onboarding_checklist and not has_customer_service:
        print('🚨 DOUBLE PROGRESSION DETECTED: customer_service was skipped!')
        print('   This indicates the task progression logic ran twice:')
        print('   1. job_analysis → customer_service (first progression)')
        print('   2. customer_service → onboarding_checklist (second progression)')
        print('   But only the final result (onboarding_checklist) was saved to messages.')
    elif has_job_analysis and has_customer_service and not has_onboarding_checklist:
        print('✅ NORMAL PROGRESSION: Currently on customer_service task')
    elif has_job_analysis and has_customer_service and has_onboarding_checklist:
        print('✅ COMPLETE PROGRESSION: All tasks present')
    else:
        print('❓ UNCLEAR STATE: Unexpected task pattern')
    
    print()
    print('=== RECOMMENDATIONS ===')
    if has_job_analysis and has_onboarding_checklist and not has_customer_service:
        print('1. The double progression issue is confirmed')
        print('2. Need to find where the second progression is happening')
        print('3. Check if process_task_completion is being called twice')
        print('4. Check for race conditions in the task completion flow')
    else:
        print('1. Create a fresh test session to reproduce the issue')
        print('2. Monitor the task progression step by step')

if __name__ == '__main__':
    debug_double_progression()
