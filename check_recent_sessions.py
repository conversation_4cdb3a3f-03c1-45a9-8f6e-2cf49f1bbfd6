#!/usr/bin/env python
import os
import sys
import django
from datetime import datetime, timedelta

# Setup Django
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

from game.models import GameSession, Message
from game.all_role_tasks import get_all_role_tasks

def check_recent_sessions():
    """Check recent junior_assistant sessions for the double progression issue"""
    
    # Get sessions from the last 24 hours
    yesterday = datetime.now() - timedelta(days=1)
    recent_sessions = GameSession.objects.filter(
        current_role='junior_assistant',
        created_at__gte=yesterday
    ).order_by('-id')
    
    print(f'=== CHECKING RECENT JUNIOR_ASSISTANT SESSIONS ===')
    print(f'Found {recent_sessions.count()} sessions in the last 24 hours')
    print()
    
    for session in recent_sessions[:10]:  # Check last 10 sessions
        print(f'--- Session {session.id} (created: {session.created_at}) ---')
        print(f'Current state: task={session.current_task}, completed={session.role_challenges_completed}')
        
        # Check what messages exist for each task
        role_tasks = get_all_role_tasks('junior_assistant')
        task_messages = {}
        
        for task in role_tasks:
            task_messages[task['id']] = session.messages.filter(task_id=task['id'], is_challenge=True).count()
        
        print(f'Task messages: {task_messages}')
        
        # Check for double progression pattern
        has_job_analysis = task_messages.get('job_analysis', 0) > 0
        has_customer_service = task_messages.get('customer_service', 0) > 0
        has_onboarding_checklist = task_messages.get('onboarding_checklist', 0) > 0
        
        if has_job_analysis and has_onboarding_checklist and not has_customer_service:
            print('🚨 DOUBLE PROGRESSION DETECTED!')
        elif has_job_analysis and has_customer_service and not has_onboarding_checklist:
            print('✅ NORMAL PROGRESSION (on customer_service)')
        elif has_job_analysis and has_customer_service and has_onboarding_checklist:
            print('✅ COMPLETE PROGRESSION')
        elif session.current_task == 'job_analysis' and session.role_challenges_completed == 0:
            print('⏳ FRESH SESSION (not started)')
        else:
            print('❓ UNCLEAR STATE')
        
        print()

if __name__ == '__main__':
    check_recent_sessions()
