#!/usr/bin/env python
import os
import sys
import django
import json

# Setup Django
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from game.models import GameSession, Message
from game.all_role_tasks import get_all_role_tasks

def test_task_progression():
    """Test the task progression issue step by step"""
    
    # Create a test user
    user, created = User.objects.get_or_create(username='test_progression_user')
    
    # Create a fresh game session
    session = GameSession.objects.create(
        user=user,
        current_role='junior_assistant',
        current_task='job_analysis',
        current_manager='manager',
        role_challenges_completed=0,
        challenges_completed=0,
        performance_score=0
    )
    
    print(f'=== CREATED FRESH TEST SESSION ===')
    print(f'Session {session.id}')
    print(f'Initial state: role={session.current_role}, task={session.current_task}, completed={session.role_challenges_completed}')
    print()
    
    # Create the Django test client
    client = Client()
    
    # Simulate login
    client.force_login(user)
    
    # Submit a good prompt for job_analysis task
    print('=== SUBMITTING PROMPT FOR JOB_ANALYSIS ===')

    payload = {
        'prompt': '''Please analyze the Junior Marketing Coordinator position at Rwenzori Innovations.

**Position Overview:**
The Junior Marketing Coordinator will support our marketing team in developing and executing digital marketing campaigns, managing social media presence, and conducting market research.

**Key Responsibilities:**
1. Assist in creating marketing content for social media platforms
2. Conduct market research and competitor analysis
3. Support email marketing campaigns and track performance metrics
4. Coordinate with design team for marketing materials
5. Maintain marketing database and customer relationship management system

**Required Skills and Qualifications:**
- Bachelor's degree in Marketing, Communications, or related field
- 1-2 years of experience in digital marketing or related role
- Proficiency in social media platforms (Facebook, Instagram, LinkedIn, Twitter)
- Experience with email marketing tools (MailChimp, Constant Contact)
- Strong analytical skills and attention to detail
- Excellent written and verbal communication skills
- Knowledge of Google Analytics and SEO principles preferred

**Recommendations for Hiring Process:**
1. Screen candidates for digital marketing portfolio and social media experience
2. Conduct skills assessment for analytics and content creation abilities
3. Include team collaboration exercise in interview process
4. Verify proficiency with marketing tools through practical demonstration
5. Check references from previous marketing or communications roles

This analysis provides a comprehensive overview of the position requirements and structured approach for identifying qualified candidates.''',
        'current_task': 'job_analysis'
    }

    response = client.post('/game/api/submit_prompt/',
                          data=json.dumps(payload),
                          content_type='application/json')
    
    print(f'Response status: {response.status_code}')
    
    if response.status_code == 200:
        response_data = response.json()
        print(f'Response grade: {response_data.get("grade", "unknown")}')
        print(f'Response meets requirements: {response_data.get("meets_requirements", "unknown")}')
        print()
        
        # Refresh the session from database
        session.refresh_from_db()
        print(f'=== SESSION STATE AFTER SUBMISSION ===')
        print(f'Current task: {session.current_task}')
        print(f'Current manager: {session.current_manager}')
        print(f'Role challenges completed: {session.role_challenges_completed}')
        print(f'Total challenges completed: {session.challenges_completed}')
        print()
        
        # Check what messages were created
        print(f'=== MESSAGES CREATED ===')
        messages = session.messages.all().order_by('timestamp')
        for msg in messages:
            print(f'  - ID {msg.id}: {msg.task_id} (challenge: {msg.is_challenge}) - {msg.text[:50]}...')
        print()
        
        # Check if customer_service was skipped
        has_job_analysis = session.messages.filter(task_id='job_analysis', is_challenge=True).exists()
        has_customer_service = session.messages.filter(task_id='customer_service', is_challenge=True).exists()
        has_onboarding_checklist = session.messages.filter(task_id='onboarding_checklist', is_challenge=True).exists()
        
        print(f'=== TASK PRESENCE CHECK ===')
        print(f'job_analysis: {"✅" if has_job_analysis else "❌"}')
        print(f'customer_service: {"✅" if has_customer_service else "❌"}')
        print(f'onboarding_checklist: {"✅" if has_onboarding_checklist else "❌"}')
        print()
        
        if session.current_task == 'customer_service':
            print('✅ SUCCESS: Correctly advanced to customer_service')
        elif session.current_task == 'onboarding_checklist':
            print('🚨 BUG REPRODUCED: Skipped customer_service, jumped to onboarding_checklist')
            print('   This confirms the double progression issue!')
        else:
            print(f'❓ UNEXPECTED: Current task is {session.current_task}')
        
        print()
        print(f'=== NEXT TASK PENDING FLAG ===')
        print(f'next_task_pending in response: {response_data.get("next_task_pending", "not present")}')
        
    else:
        print(f'❌ Request failed with status {response.status_code}')
        print(f'Response: {response.content.decode()}')
    
    # Clean up
    session.delete()
    user.delete()

if __name__ == '__main__':
    test_task_progression()
