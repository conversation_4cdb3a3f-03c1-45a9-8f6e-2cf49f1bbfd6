# Reset Button Implementation Guide - Functional Mechanics

This document explains how reset buttons work functionally in the prompt game project, focusing on the actual mechanisms that reset game state, data structures, and system behavior.

## Overview

The project implements comprehensive reset functionality that operates at multiple levels:
1. **Frontend State Reset** - JavaScript variables, UI elements, and client-side storage
2. **Backend State Reset** - Database records, session data, and server-side game state
3. **Full System Reset** - Complete game restart with fresh initialization

## 1. Complete Game Reset Flow

### The Reset Trigger Chain

When a user clicks the reset button, this is the complete flow that occurs:

1. **User Confirmation** → 2. **Frontend Cleanup** → 3. **Backend Reset** → 4. **Fresh Initialization**

### Frontend Reset Mechanics (`game/static/game/js/context_aware_game.js`)

#### Game State Object Reset

```javascript
// Complete game state reset - all variables cleared
function resetGameState() {
    gameState.messages = [];                    // Clear all chat messages
    gameState.currentRole = 'applicant';        // Reset to starting role
    gameState.performanceScore = 0;             // Reset score to zero
    gameState.challengesCompleted = 0;          // Reset challenge counter
    gameState.roleChallengesCompleted = 0;      // Reset role-specific challenges
    gameState.gameCompleted = false;            // Mark game as not completed
    gameState.currentManager = 'hr';            // Reset to HR manager
    gameState.currentTask = 'cover_letter';     // Reset to first task
    gameState.completedRoles = [];              // Clear completed roles array
    gameState.firstTaskPending = true;          // Reset task pending flag
    gameState.nextTaskPending = false;          // Clear next task flag
    gameState.taskCompleted = false;            // Reset task completion flag
}
```

#### UI Element Reset

```javascript
// Clear all UI elements and restore initial state
function resetUIElements() {
    // 1. Clear message container completely
    if (elements.messagesContainer) {
        elements.messagesContainer.innerHTML = '';
        console.log('Cleared message container in UI');
    }

    // 2. Reset input fields
    if (elements.promptInput) {
        elements.promptInput.value = '';
    }

    // 3. Reset button states
    if (elements.submitButton) {
        elements.submitButton.disabled = false;
    }

    // 4. Hide any modal dialogs
    elements.gameCompleteModal.classList.add('hidden');

    // 5. Reset step visibility
    setStep('prompt'); // Return to initial step
}
```

#### The Complete Reset Function

```javascript
// This is the main reset function that orchestrates everything
async function startGame() {
    console.log('Starting game...');
    showLoading();

    // STEP 1: Clear UI immediately
    if (elements.messagesContainer) {
        elements.messagesContainer.innerHTML = '';
    }

    // STEP 2: Reset all game state variables
    gameState.messages = [];
    gameState.currentRole = 'applicant';
    gameState.performanceScore = 0;
    gameState.challengesCompleted = 0;
    gameState.roleChallengesCompleted = 0;
    gameState.gameCompleted = false;
    gameState.currentManager = 'hr';
    gameState.currentTask = 'cover_letter';
    gameState.completedRoles = [];
    gameState.firstTaskPending = true;
    gameState.nextTaskPending = false;
    gameState.taskCompleted = false;

    // STEP 3: Make API call to reset backend
    try {
        const timestamp = new Date().getTime();
        const response = await fetch(`${API_ENDPOINTS.START_GAME}?t=${timestamp}`, {
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // STEP 4: Update frontend with fresh backend data
        gameState.currentRole = data.current_role || 'applicant';
        gameState.performanceScore = data.performance_score || 0;
        gameState.challengesCompleted = data.challenges_completed || 0;
        gameState.gameCompleted = data.game_completed || false;
        gameState.currentManager = data.current_manager || 'hr';
        gameState.currentTask = data.current_task || 'cover_letter';
        gameState.completedRoles = data.completed_roles || [];

        // STEP 5: Initialize fresh UI
        updateUI();

    } catch (error) {
        console.error('Error starting game:', error);
    } finally {
        hideLoading();
    }
}
```

## 2. Backend Reset Mechanics (`game/views.py`)

### Database State Reset

The backend reset happens in the Django view when the frontend calls the start_game endpoint:

```python
def start_game(request):
    """
    API endpoint to start a new game - resets all database state
    """
    try:
        # Check if this is a restart request
        is_restart = 'restart' in request.GET or 't' in request.GET
        logging.info(f"Starting game with restart={is_restart}")

        # Get or create game session from database
        game_session = get_or_create_game_session(request)

        # CRITICAL: Reset ALL database fields to initial state
        game_session.current_role = "applicant"           # Reset role progression
        game_session.performance_score = 0                # Reset score to zero
        game_session.challenges_completed = 0             # Reset challenge count
        game_session.role_challenges_completed = 0        # Reset role-specific challenges
        game_session.game_completed = False               # Mark as not completed
        game_session.current_manager = "hr"               # Reset to HR manager
        game_session.current_task = "cover_letter"        # Reset to first task
        game_session.set_completed_roles([])              # Clear completed roles list
        game_session.first_task_pending = True            # Reset task flags

        if hasattr(game_session, 'next_task_pending'):
            game_session.next_task_pending = False

        # SAVE to database - this persists the reset
        game_session.save()

        # Return fresh state to frontend
        return JsonResponse({
            'success': True,
            'current_role': game_session.current_role,
            'performance_score': game_session.performance_score,
            'challenges_completed': game_session.challenges_completed,
            'game_completed': game_session.game_completed,
            'current_manager': game_session.current_manager,
            'current_task': game_session.current_task,
            'completed_roles': game_session.get_completed_roles(),
            'first_task_pending': game_session.first_task_pending
        })

    except Exception as e:
        logging.error(f"Error starting game: {str(e)}")
        return JsonResponse({'success': False, 'message': str(e)})
```

### Game State Initialization (`game/game_state.py`)

```python
def initialize_game_state():
    """
    Initialize a completely fresh game state.
    This defines what "reset" means for the game.
    """
    game_state = {
        "current_role": "applicant",              # Starting role
        "performance_score": 0,                   # Zero score
        "challenges_completed": 0,                # No challenges completed
        "role_challenges_completed": 0,           # No role challenges completed
        "messages": [],                           # Empty message history
        "game_completed": False,                  # Game not completed
        "current_manager": "hr",                  # Start with HR manager
        "current_task": "cover_letter",           # First task
        "completed_roles": [],                    # No roles completed
        "first_task_pending": True                # First task should be delayed
    }

    return game_state
```

## 3. Organization Chart Reset Mechanics

### Org Chart State Reset (`game/static/game/js/org-chart.js`)

```javascript
// Reset the organizational chart game state
function resetGame() {
    // Reset the org chart state object
    orgChartState = {
        currentPosition: "applicant",      // Back to starting position
        completedTasks: [],               // Clear all completed tasks
        completedPositions: [],           // Clear all completed positions
        currentTaskIndex: 0               // Reset task index to first task
    };

    // Save the reset state to localStorage
    saveGameState();

    // Reinitialize the org chart with fresh state
    initOrgChart();
}

// Save state to localStorage for persistence
function saveGameState() {
    localStorage.setItem('orgChartState', JSON.stringify(orgChartState));
}

// Initialize org chart from saved or default state
function initGameState() {
    const savedState = localStorage.getItem('orgChartState');
    if (savedState) {
        orgChartState = JSON.parse(savedState);
    } else {
        // Use default initial state if no saved state
        orgChartState = {
            currentPosition: "applicant",
            completedTasks: [],
            completedPositions: [],
            currentTaskIndex: 0
        };
    }
}
```

## 4. Reset Confirmation and User Experience

### Confirmation Modal Implementation

```javascript
// Show restart confirmation modal with proper event handling
function showRestartConfirmation() {
    const restartConfirmationModal = document.getElementById('restart-confirmation-modal');
    if (restartConfirmationModal) {
        restartConfirmationModal.classList.remove('hidden');

        // Handle confirmation button
        const confirmButton = document.getElementById('confirm-restart-button');
        if (confirmButton) {
            // Clone button to remove any existing event listeners
            const newConfirmButton = confirmButton.cloneNode(true);
            confirmButton.parentNode.replaceChild(newConfirmButton, confirmButton);

            // Add fresh event listener
            newConfirmButton.addEventListener('click', () => {
                console.log('Restart confirmed by user');
                restartConfirmationModal.classList.add('hidden');

                // Execute the actual reset
                performCompleteReset();
            });
        }
    }
}

// The actual reset function that does all the work
function performCompleteReset() {
    // 1. Clear UI immediately for instant feedback
    if (elements.messagesContainer) {
        elements.messagesContainer.innerHTML = '';
    }

    // 2. Reset JavaScript game state
    gameState.messages = [];

    // 3. Call backend to reset database
    startGame(); // This makes the API call to reset backend
}
```

## How to Replicate This Reset System

### Step 1: Define Your Reset Scope

First, identify what needs to be reset in your application:

```javascript
// Define what constitutes "reset" for your application
const resetScope = {
    // Frontend state
    uiElements: ['message-container', 'input-field', 'progress-bar'],
    jsVariables: ['gameState', 'userProgress', 'currentLevel'],
    localStorage: ['gameData', 'userPreferences'],
    sessionStorage: ['tempData', 'currentSession'],

    // Backend state
    databaseFields: ['score', 'level', 'completed_tasks'],
    sessionData: ['user_session', 'game_session'],

    // UI state
    modalDialogs: ['game-modal', 'settings-modal'],
    formInputs: ['user-input', 'settings-form'],
    visualElements: ['charts', 'progress-indicators']
};
```

### Step 2: Create the Reset Function Architecture

```javascript
// Main reset orchestrator function
async function resetApplication() {
    try {
        // Step 1: Show loading state
        showLoadingIndicator();

        // Step 2: Reset frontend immediately
        resetFrontendState();

        // Step 3: Reset backend via API
        await resetBackendState();

        // Step 4: Reinitialize application
        await initializeApplication();

        // Step 5: Update UI with fresh state
        updateUIWithFreshState();

    } catch (error) {
        console.error('Reset failed:', error);
        handleResetError(error);
    } finally {
        hideLoadingIndicator();
    }
}

// Frontend reset function
function resetFrontendState() {
    // Clear all JavaScript variables
    window.appState = getInitialState();

    // Clear storage
    localStorage.removeItem('appData');
    sessionStorage.clear();

    // Reset UI elements
    document.querySelectorAll('.user-content').forEach(el => {
        el.innerHTML = '';
    });

    // Reset form inputs
    document.querySelectorAll('input, textarea, select').forEach(input => {
        input.value = input.defaultValue || '';
    });
}

// Backend reset function
async function resetBackendState() {
    const response = await fetch('/api/reset', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({ resetType: 'complete' })
    });

    if (!response.ok) {
        throw new Error(`Reset failed: ${response.status}`);
    }

    return response.json();
}
```

### Step 3: Backend Reset Implementation

```python
# Django view for handling reset
def reset_application(request):
    """
    Reset all application state for the current user/session
    """
    try:
        if request.method == 'POST':
            # Get user session or create new one
            user_session = get_or_create_user_session(request)

            # Reset all relevant database fields
            user_session.score = 0
            user_session.level = 1
            user_session.completed_tasks = []
            user_session.current_progress = 0
            user_session.is_completed = False
            user_session.last_activity = timezone.now()

            # Clear any related data
            user_session.game_data = {}
            user_session.preferences = get_default_preferences()

            # Save to database
            user_session.save()

            # Return fresh state
            return JsonResponse({
                'success': True,
                'state': {
                    'score': user_session.score,
                    'level': user_session.level,
                    'completed_tasks': user_session.completed_tasks,
                    'current_progress': user_session.current_progress
                }
            })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)
```

### Step 4: Error Handling and Recovery

```javascript
// Robust error handling for reset operations
function handleResetError(error) {
    console.error('Reset operation failed:', error);

    // Try to recover gracefully
    try {
        // Attempt partial reset if full reset failed
        resetFrontendState();

        // Show user-friendly error message
        showErrorMessage('Reset partially completed. Please refresh the page.');

    } catch (recoveryError) {
        console.error('Recovery also failed:', recoveryError);

        // Last resort: suggest page refresh
        if (confirm('Reset failed. Would you like to refresh the page?')) {
            window.location.reload();
        }
    }
}

// Validation before reset
function validateResetConditions() {
    // Check if reset is safe to perform
    const checks = [
        () => document.readyState === 'complete',
        () => !window.operationInProgress,
        () => navigator.onLine
    ];

    return checks.every(check => check());
}
```

## Key Functional Principles

1. **Immediate UI Feedback**: Clear UI elements first for instant user feedback
2. **State Synchronization**: Ensure frontend and backend state match after reset
3. **Error Recovery**: Always have fallback mechanisms for failed resets
4. **Validation**: Check conditions before allowing reset operations
5. **Logging**: Track reset operations for debugging and analytics
6. **User Confirmation**: Always confirm destructive operations
7. **Progressive Reset**: Reset in stages (UI → Frontend → Backend → Reinitialize)

This functional approach ensures reliable, user-friendly reset behavior that maintains data integrity and provides excellent user experience.
