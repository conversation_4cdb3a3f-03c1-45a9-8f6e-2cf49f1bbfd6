# Reset Button Implementation Guide

This document explains how reset buttons are implemented in the prompt game project and provides guidance for replicating this functionality in other projects.

## Overview

The project implements multiple types of reset functionality across different components:
1. **Chat Conversation Reset** - Clears chat messages and restores initial state
2. **Game Reset** - Resets game state and progress
3. **Zoom Reset** - Resets chart/diagram zoom levels
4. **Form Reset** - Resets form controls to default values

## 1. Chat Conversation Reset

### HTML Structure

The reset button is placed in the chat interface with a centered layout:

```html
<!-- Reset Button - Centered -->
<div class="d-flex justify-content-center general-reset-area mb-3">
    <button type="button" id="reset-chat-btn" class="btn btn-outline-secondary btn-sm">
        Reset Conversation
    </button>
</div>
```

### JavaScript Implementation

#### Basic Reset Function (`static1/js/button-functionality.js`)

```javascript
/**
 * Initialize reset conversation button
 */
function initResetButton() {
    const resetButton = document.getElementById('reset-chat-btn');

    if (resetButton) {
        // Remove any existing event listeners to prevent duplicates
        const newResetButton = resetButton.cloneNode(true);
        resetButton.parentNode.replaceChild(newResetButton, resetButton);

        // Add new event listener
        newResetButton.addEventListener('click', function() {
            // Confirm before resetting
            if (confirm('Are you sure you want to reset the conversation? This will clear all messages.')) {
                resetConversation();
            }
        });
    }
}

/**
 * Reset the conversation
 */
function resetConversation() {
    const chatBox = document.getElementById('chat-box');
    const initialDisplay = document.getElementById('initial-display-area');

    if (chatBox) {
        // Remove all messages except initial greeting
        const messages = chatBox.querySelectorAll('.message:not(.initial-greeting)');
        messages.forEach(message => {
            message.remove();
        });

        // Show initial display area
        if (initialDisplay) {
            initialDisplay.style.display = 'block';
        }

        // Clear message history
        window.messageHistory = [];
    }
}
```

#### Advanced Reset with Session Storage (`templates1/assistants/assistant_chat.html`)

```javascript
// Reset button functionality
if (resetBtn && chatBox) {
    resetBtn.addEventListener('click', () => {
        // Directly reset without confirmation dialog
        sessionStorage.removeItem(sessionStorageKey); // Clear history
        // Restore initial state using the stored HTML
        chatBox.innerHTML = initialDisplayAreaHTML;
        // Reset the last clicked item tracking
        lastClickedNavItem = null;
        lastClickTimestamp = 0;
        // Clear any stored navigation content
        currentNavContext = '';
        // Re-run the nav setup logic for the restored initial state
        setupNavigation();
        // Clear input and reset button state
        if(messageInput) messageInput.value = '';
        if(sendButton) sendButton.disabled = true;
        if(messageInput) messageInput.focus();
    });
}
```

### CSS Styling

#### General Reset Button Styling (`static1/css/style.css`)

```css
/* Style for Reset button in general layout */
.general-reset-area {
    max-width: 720px;
    margin: 1rem auto;
    text-align: center;
}

.general-reset-area button {
    background-color: #f8f9fa;
    color: #000000;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    padding: 0.5rem 1.25rem;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.general-reset-area button:hover {
    background-color: #000000;
    color: #ffffff;
    border-color: #000000;
    transform: translateY(-1px);
}

.general-reset-area button:active {
    transform: translateY(1px);
    background-color: #dee2e6;
}
```

#### Enhanced Button Styling (`static1/css/assistant-buttons-fix.css`)

```css
/* Reset conversation button */
.reset-conversation-btn,
button[data-action="reset"],
button.reset-conversation {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
    color: #ffffff !important;
    cursor: pointer !important;
    pointer-events: auto !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.reset-conversation-btn:hover,
button[data-action="reset"]:hover,
button.reset-conversation:hover {
    background-color: #218838 !important;
    border-color: #1e7e34 !important;
}
```

## 2. Game Reset Implementation

### Game State Reset (`game/static/game/js/context_aware_game.js`)

```javascript
// Show restart confirmation modal
function showRestartConfirmation() {
    const restartConfirmationModal = document.getElementById('restart-confirmation-modal');
    if (restartConfirmationModal) {
        restartConfirmationModal.classList.remove('hidden');
        
        // Handle confirmation
        const confirmButton = document.getElementById('confirm-restart-button');
        if (confirmButton) {
            // Clone button to remove existing listeners
            const newConfirmButton = confirmButton.cloneNode(true);
            confirmButton.parentNode.replaceChild(newConfirmButton, confirmButton);

            newConfirmButton.addEventListener('click', () => {
                restartConfirmationModal.classList.add('hidden');
                
                // Clear UI messages immediately
                if (elements.messagesContainer) {
                    elements.messagesContainer.innerHTML = '';
                }
                
                // Reset game state messages array
                gameState.messages = [];
                
                // Start a new game
                startGame();
            });
        }
    }
}
```

### Organization Chart Reset (`game/static/game/js/org-chart.js`)

```javascript
// Reset the game
function resetGame() {
    orgChartState = {
        currentPosition: "applicant",
        completedTasks: [],
        completedPositions: [],
        currentTaskIndex: 0
    };

    saveGameState();
    initOrgChart();
}
```

## 3. Zoom Reset Implementation

### Chart Zoom Reset (`game/static/game/js/chart-hover-zoom.js`)

```javascript
// Create reset button
const resetBtn = document.createElement('button');
resetBtn.className = 'zoom-control-btn zoom-reset';
resetBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path><path d="M3 3v5h5"></path></svg>';
resetBtn.title = 'Reset Zoom';
resetBtn.addEventListener('click', function() {
    setScale(contentElement, 1);
});
```

## Implementation Steps for Other Projects

### Step 1: HTML Structure
1. Create a button element with a unique ID
2. Use appropriate Bootstrap classes for styling
3. Position the button logically in your interface

### Step 2: JavaScript Initialization
1. Create an initialization function that runs on DOM load
2. Use event delegation or clone buttons to avoid duplicate listeners
3. Implement confirmation dialogs for destructive actions

### Step 3: Reset Logic
1. Identify what needs to be reset (DOM elements, variables, storage)
2. Clear relevant data structures
3. Restore initial UI state
4. Reset any tracking variables

### Step 4: CSS Styling
1. Create consistent button styling
2. Add hover and active states
3. Ensure accessibility with proper contrast
4. Consider mobile responsiveness

### Step 5: Error Handling
1. Check for element existence before manipulation
2. Provide fallback behavior
3. Log errors for debugging

## Key Patterns Used

1. **Event Listener Management**: Clone buttons to remove existing listeners
2. **Confirmation Dialogs**: Use `confirm()` or custom modals for destructive actions
3. **State Management**: Clear both UI and JavaScript state
4. **Progressive Enhancement**: Check for element existence before acting
5. **Accessibility**: Proper button semantics and keyboard support

## Best Practices

1. Always confirm destructive actions
2. Provide visual feedback during reset operations
3. Restore focus to appropriate elements after reset
4. Clear both client-side and session storage as needed
5. Use semantic HTML for better accessibility
6. Implement proper error handling
7. Test reset functionality across different states

This implementation provides a robust, user-friendly reset system that can be adapted to various project needs.
