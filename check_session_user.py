#!/usr/bin/env python
import os
import sys
import django

# Setup Django
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

from game.models import GameSession

def check_session_user():
    """Check the user for session 46"""
    
    try:
        session = GameSession.objects.get(id=46)
        print(f'Session 46:')
        print(f'  User: {session.user}')
        print(f'  User ID: {session.user.id if session.user else "None"}')
        print(f'  Username: {session.user.username if session.user else "None"}')
        print(f'  Current task: {session.current_task}')
        print(f'  Role challenges completed: {session.role_challenges_completed}')
    except GameSession.DoesNotExist:
        print("Session 46 not found")

if __name__ == '__main__':
    check_session_user()
